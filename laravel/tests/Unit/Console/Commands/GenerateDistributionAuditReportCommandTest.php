<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\GenerateDistributionAuditReportCommand;
use App\LineDivParent;
use App\LineDivision;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Tests\TestCase;

/**
 * Test class for GenerateDistributionAuditReportCommand
 * 
 * Tests the comprehensive distribution audit report generation including
 * validation of distribution integrity, hierarchy checks, split analysis,
 * ceiling processing, and referential integrity.
 * 
 * @covers \App\Console\Commands\GenerateDistributionAuditReportCommand
 */
class GenerateDistributionAuditReportCommandTest extends TestCase
{
    private string $fromDate;
    private string $toDate;

    protected function setUp(): void
    {
        parent::setUp();

        $this->fromDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->toDate = Carbon::now()->format('Y-m-d');

        // Mock storage for file exports
        Storage::fake('local');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test command signature and basic structure
     */
    public function test_command_signature(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        
        $this->assertStringContainsString('distribution:generate-audit-report', $command->getName());
        $this->assertStringContainsString('Generate comprehensive audit report', $command->getDescription());
    }

    /**
     * Test command requires date parameters
     */
    public function test_command_requires_date_parameters(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report');
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Both --from-date and --to-date are required', Artisan::output());
    }

    /**
     * Test command validates date format
     */
    public function test_command_validates_date_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => 'invalid-date',
            '--to-date' => '2024-01-31'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Invalid date format', Artisan::output());
    }

    /**
     * Test command validates date range
     */
    public function test_command_validates_date_range(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => '2024-01-31',
            '--to-date' => '2024-01-01'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('from-date cannot be after to-date', Artisan::output());
    }

    /**
     * Test command validates distribution type
     */
    public function test_command_validates_distribution_type(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--distribution-type' => '5'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('distribution-type must be 1, 2, or 3', Artisan::output());
    }

    /**
     * Test command validates output format
     */
    public function test_command_validates_output_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'invalid'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('format must be table, json, or csv', Artisan::output());
    }

    /**
     * Test basic command execution with minimal data
     */
    public function test_basic_command_execution(): void
    {
        // Mock Sale model to return empty results for basic execution
        $this->mockSaleModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--summary-only' => true
        ]);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('DISTRIBUTION AUDIT SUMMARY', Artisan::output());
    }

    /**
     * Test command with JSON output format
     */
    public function test_json_output_format(): void
    {
        $this->mockSaleModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'json'
        ]);

        $this->assertEquals(0, $exitCode);

        $output = Artisan::output();
        $this->assertJson($output);

        $data = json_decode($output, true);
        $this->assertArrayHasKey('metadata', $data);
        $this->assertArrayHasKey('summary', $data);
        $this->assertArrayHasKey('distribution_integrity', $data);
    }

    /**
     * Test command with CSV output format
     */
    public function test_csv_output_format(): void
    {
        $this->mockSaleModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'csv'
        ]);

        $this->assertEquals(0, $exitCode);

        $output = Artisan::output();
        $this->assertStringContainsString('DISTRIBUTION AUDIT REPORT (CSV FORMAT)', $output);
        $this->assertStringContainsString('Category","Type","Sale ID"', $output);
        $this->assertStringContainsString('Summary Statistics:', $output);
    }

    /**
     * Test command with file export
     */
    public function test_file_export(): void
    {
        $this->mockSaleModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'json',
            '--export-file' => 'test_audit'
        ]);

        $this->assertEquals(0, $exitCode);

        // Check if file was created
        $files = Storage::disk('local')->files('distribution_audit_reports');
        $this->assertNotEmpty($files);

        $exportedFile = collect($files)->first(function ($file) {
            return str_contains($file, 'test_audit') && str_ends_with($file, '.json');
        });

        $this->assertNotNull($exportedFile);

        // Verify file content
        $content = Storage::disk('local')->get($exportedFile);
        $data = json_decode($content, true);
        $this->assertArrayHasKey('metadata', $data);
    }

    /**
     * Test CSV export with file
     */
    public function test_csv_file_export(): void
    {
        $this->mockSaleModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'csv',
            '--export-file' => 'test_csv_audit'
        ]);

        $this->assertEquals(0, $exitCode);

        // Check if CSV file was created
        $files = Storage::disk('local')->files('distribution_audit_reports');
        $csvFile = collect($files)->first(function ($file) {
            return str_contains($file, 'test_csv_audit') && str_ends_with($file, '.csv');
        });

        $this->assertNotNull($csvFile);

        // Verify CSV file content
        $content = Storage::disk('local')->get($csvFile);
        $this->assertStringContainsString('Category","Type","Sale ID"', $content);

        // Verify terminal output also shows CSV data
        $output = Artisan::output();
        $this->assertStringContainsString('CSV data also displayed below:', $output);
        $this->assertStringContainsString('DISTRIBUTION AUDIT REPORT (CSV FORMAT)', $output);
    }

    /**
     * Test command with all analysis options enabled
     */
    public function test_comprehensive_analysis(): void
    {
        $this->mockSaleModel();
        $this->mockSaleDetailModel();
        $this->mockLineDivisionModel();
        $this->mockLineDivParentModel();

        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--include-hierarchy' => true,
            '--include-split-analysis' => true,
            '--include-ceiling-analysis' => true,
            '--include-referential' => true,
            '--format' => 'json'
        ]);

        $this->assertEquals(0, $exitCode);

        $output = Artisan::output();
        $data = json_decode($output, true);

        $this->assertArrayHasKey('hierarchy_validation', $data);
        $this->assertArrayHasKey('split_analysis', $data);
        $this->assertArrayHasKey('ceiling_analysis', $data);
        $this->assertArrayHasKey('referential_integrity', $data);
        $this->assertArrayHasKey('recommendations', $data);
    }

    /**
     * Test Store strategy specific analysis
     */
    public function test_store_strategy_analysis(): void
    {
        $this->createTestSalesData();
        
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--distribution-type' => '2',
            '--include-split-analysis' => true,
            '--format' => 'json'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        $output = Artisan::output();
        $data = json_decode($output, true);
        
        $this->assertArrayHasKey('split_analysis', $data);
        $this->assertEquals('2', $data['metadata']['distribution_type']);
    }

    /**
     * Mock Sale model for testing
     */
    private function mockSaleModel(): void
    {
        $this->partialMock(Sale::class, function ($mock) {
            // Mock basic query methods
            $mock->shouldReceive('whereBetween')
                ->with('date', [$this->fromDate, $this->toDate])
                ->andReturnSelf();
            $mock->shouldReceive('whereIn')
                ->andReturnSelf();
            $mock->shouldReceive('where')
                ->andReturnSelf();
            $mock->shouldReceive('selectRaw')
                ->andReturnSelf();
            $mock->shouldReceive('groupBy')
                ->andReturnSelf();
            $mock->shouldReceive('orderByDesc')
                ->andReturnSelf();
            $mock->shouldReceive('limit')
                ->andReturnSelf();
            $mock->shouldReceive('count')
                ->andReturn(0);
            $mock->shouldReceive('get')
                ->andReturn(collect([]));
            $mock->shouldReceive('whereDoesntHave')
                ->andReturnSelf();
        });
    }

    /**
     * Mock SaleDetail model for testing
     */
    private function mockSaleDetailModel(): void
    {
        $this->partialMock(SaleDetail::class, function ($mock) {
            $mock->shouldReceive('whereHas')
                ->andReturnSelf();
            $mock->shouldReceive('where')
                ->andReturnSelf();
            $mock->shouldReceive('get')
                ->andReturn(collect([]));
            $mock->shouldReceive('count')
                ->andReturn(0);
        });
    }

    /**
     * Mock LineDivision model for testing
     */
    private function mockLineDivisionModel(): void
    {
        $this->partialMock(LineDivision::class, function ($mock) {
            $mock->shouldReceive('count')
                ->andReturn(0);
            $mock->shouldReceive('get')
                ->andReturn(collect([]));
        });
    }

    /**
     * Mock LineDivParent model for testing
     */
    private function mockLineDivParentModel(): void
    {
        $this->partialMock(LineDivParent::class, function ($mock) {
            $mock->shouldReceive('whereNull')
                ->andReturnSelf();
            $mock->shouldReceive('count')
                ->andReturn(0);
            $mock->shouldReceive('get')
                ->andReturn(collect([]));
        });
    }
}
